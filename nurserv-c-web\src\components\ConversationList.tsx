import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageSquare, User } from 'lucide-react';
import { format, isValid } from 'date-fns';
import useWebSocketChat from '@/hooks/useWebSocketChat';
import { Conversation } from '@/store/api/chatApiSlice';

interface ConversationListProps {
  userId: string;
  userType: 'nurse' | 'patient';
  userName: string;
  onConversationSelect?: (conversation: Conversation) => void;
  className?: string;
}

const ConversationList: React.FC<ConversationListProps> = ({
  userId,
  userType,
  userName,
  onConversationSelect,
  className = '',
}) => {
  const navigate = useNavigate();

  const baseWsUrl =
    import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws')
    ? baseWsUrl
    : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  const webSocketChat = useWebSocketChat({
    url: wsUrl,
    token,
    userId,
    userType,
    userName,
    enabled: true,
  });

  const { conversations, loading, errors, useGetConversationsQuery } = webSocketChat;

  const {
    data: conversationsData,
    isLoading,
    error,
    refetch,
  } = useGetConversationsQuery(
    { page: 1, limit: 50, status: 'active' },
    { skip: false }
  );

  // Auto-refresh conversations periodically
  useEffect(() => {
    const interval = setInterval(() => {
      refetch();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [refetch]);

  const handleConversationClick = (conversation: Conversation) => {
    if (onConversationSelect) {
      onConversationSelect(conversation);
    } else {
      navigate(`/chat/${conversation.id}`, {
        state: {
          nurseName: userType === 'patient' ? conversation.nurseName : conversation.patientName,
          nurseId: userType === 'patient' ? conversation.nurseId : conversation.patientId,
        },
      });
    }
  };

  const formatLastMessageTime = (timestamp?: string) => {
    if (!timestamp) return '';
    
    try {
      const date = new Date(timestamp);
      if (!isValid(date)) return '';
      
      const now = new Date();
      const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);
      
      if (diffInHours < 24) {
        return format(date, 'HH:mm');
      } else if (diffInHours < 168) { // 7 days
        return format(date, 'EEE');
      } else {
        return format(date, 'MMM dd');
      }
    } catch {
      return '';
    }
  };

  if (isLoading && conversations.length === 0) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-nursery-blue mx-auto"></div>
          <p className="mt-2 text-sm text-gray-600">Loading conversations...</p>
        </div>
      </div>
    );
  }

  if (error && conversations.length === 0) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">Failed to load conversations</p>
          <button
            onClick={() => refetch()}
            className="mt-2 text-nursery-blue hover:underline"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  if (conversations.length === 0) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="text-center">
          <MessageSquare className="h-12 w-12 mx-auto mb-4 text-gray-400" />
          <p className="text-gray-600">No conversations yet</p>
          <p className="text-sm text-gray-500 mt-1">
            Start a conversation with a {userType === 'patient' ? 'nurse' : 'patient'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {conversations.map((conversation) => {
        const otherPersonName = userType === 'patient' 
          ? conversation.nurseName 
          : conversation.patientName;
        
        const hasUnread = (conversation.unreadCount || 0) > 0;

        return (
          <button
            key={conversation.id}
            onClick={() => handleConversationClick(conversation)}
            className="w-full flex items-center p-4 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors text-left"
          >
            <div className="w-12 h-12 bg-nursery-blue rounded-full flex items-center justify-center mr-3 flex-shrink-0">
              <User className="w-6 h-6 text-white" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-1">
                <h3 className={`font-medium truncate ${hasUnread ? 'font-semibold' : ''}`}>
                  {otherPersonName}
                </h3>
                <div className="flex items-center space-x-2 flex-shrink-0">
                  {hasUnread && (
                    <span className="bg-nursery-blue text-white text-xs rounded-full px-2 py-1 min-w-[20px] text-center">
                      {conversation.unreadCount}
                    </span>
                  )}
                  <span className="text-xs text-gray-500">
                    {formatLastMessageTime(conversation.lastMessageTime)}
                  </span>
                </div>
              </div>
              
              {conversation.lastMessage && (
                <p className={`text-sm text-gray-600 truncate ${hasUnread ? 'font-medium' : ''}`}>
                  {conversation.lastMessage}
                </p>
              )}
              
              <div className="flex items-center justify-between mt-1">
                <span className="text-xs text-gray-400 capitalize">
                  {conversation.status}
                </span>
              </div>
            </div>
          </button>
        );
      })}
    </div>
  );
};

export default ConversationList;

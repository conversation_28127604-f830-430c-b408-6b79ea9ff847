const express = require('express');
const router = express.Router();
const {
  createConversation,
  sendMessage,
  getMessages,
  getUserConversations,
  getConversation,
  markConversationInactive,
  getActiveConversationsCount,
  searchMessages,
  getConversationStats,
  markMessagesAsRead,
  getConversationWithMessagesEndpoint,
  getNurseInfo,
  getWebSocketStatus,
  updateConversationStatus,
  messageValidation,
  conversationValidation
} = require('../controller/chatController');

const {
  authenticateUser,
  requirePatientOrNurse,
  validateConversationAccess
} = require('../middleware/auth');

// Apply authentication middleware to all routes
router.use(authenticateUser);

// Core conversation routes
router.post(
  '/conversations',
  requirePatientOrNurse, // Allow both patients and nurses to create conversations
  conversationValidation,
  createConversation
);
router.get('/conversations', getUserConversations);
router.get('/conversations/:conversationId', validateConversationAccess, getConversation);
router.get('/conversations/:conversationId/with-messages', validateConversationAccess, getConversationWithMessagesEndpoint);
router.patch('/conversations/:conversationId/inactive', validateConversationAccess, markConversationInactive);

// Message routes
router.post('/conversations/:conversationId/messages', validateConversationAccess, messageValidation, sendMessage);
router.get('/conversations/:conversationId/messages', validateConversationAccess, getMessages);
router.get('/conversations/:conversationId/messages/search', validateConversationAccess, searchMessages);
router.post('/conversations/:conversationId/messages/read', validateConversationAccess, markMessagesAsRead);

// Route alias for backward compatibility with frontend
router.patch('/conversations/:conversationId/read', validateConversationAccess, markMessagesAsRead);

// Update conversation status
router.put('/conversations/:conversationId/status', validateConversationAccess, updateConversationStatus);

// Nurse info endpoint
router.get('/nurses/:nurseId', getNurseInfo);

// WebSocket status endpoint
router.get('/websocket/status', getWebSocketStatus);

// Analytics and scrutiny routes
router.get('/conversations/:conversationId/stats', validateConversationAccess, getConversationStats);
router.get('/conversations/active-count', getActiveConversationsCount);

module.exports = router;
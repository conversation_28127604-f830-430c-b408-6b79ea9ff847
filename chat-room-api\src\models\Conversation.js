const mongoose = require('mongoose');

const conversationSchema = new mongoose.Schema({
  customerId: {
    type: String,
    required: true,
    index: true
  },
  nurseId: {
    type: String,
    required: true,
    index: true
  },
  title: {
    type: String,
    default: null
  },
  status: {
    type: String,
    enum: ['active', 'inactive', 'archived'],
    default: 'active',
    index: true
  },
  lastMessageAt: {
    type: Date,
    default: Date.now,
    index: true
  },
  messageCount: {
    type: Number,
    default: 0
  },
  metadata: {
    type: Map,
    of: String,
    default: {}
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for efficient queries
conversationSchema.index({ customerId: 1, lastMessageAt: -1 });
conversationSchema.index({ nurseId: 1, lastMessageAt: -1 });
conversationSchema.index({ status: 1, lastMessageAt: -1 });

// Virtual for getting the last message
conversationSchema.virtual('lastMessage', {
  ref: 'Message',
  localField: '_id',
  foreignField: 'conversationId',
  justOne: true,
  options: { sort: { createdAt: -1 } }
});

// Pre-save middleware to update lastMessageAt
conversationSchema.pre('save', function(next) {
  if (this.isModified('messageCount')) {
    this.lastMessageAt = new Date();
  }
  next();
});

// Static method to create conversation
conversationSchema.statics.createConversation = async function(customerId, nurseId, title = null) {
  const conversation = new this({
    customerId,
    nurseId,
    title,
    status: 'active'
  });
  
  return await conversation.save();
};

// Static method to get user conversations
conversationSchema.statics.getUserConversations = async function(userId, userType, page = 1, limit = 20) {
  const skip = (page - 1) * limit;
  const userField = userType === 'patient' ? 'customerId' : 'nurseId';
  
  return await this.find({ [userField]: userId })
    .sort({ lastMessageAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('lastMessage')
    .lean();
};

// Static method to get active conversations count
conversationSchema.statics.getActiveConversationsCount = async function(userId, userType) {
  const userField = userType === 'patient' ? 'customerId' : 'nurseId';
  
  return await this.countDocuments({
    [userField]: userId,
    status: 'active'
  });
};

// Instance method to mark as inactive
conversationSchema.methods.markInactive = async function() {
  this.status = 'inactive';
  return await this.save();
};

// Instance method to archive
conversationSchema.methods.archive = async function() {
  this.status = 'archived';
  return await this.save();
};

// Static method to get conversation by ID
conversationSchema.statics.getConversation = async function(conversationId) {
  return await this.findById(conversationId)
    .populate('lastMessage')
    .lean();
};

// Static method to update conversation status
conversationSchema.statics.updateConversationStatus = async function(conversationId, status) {
  const conversation = await this.findById(conversationId);

  if (!conversation) {
    return null;
  }

  const previousStatus = conversation.status;
  conversation.status = status;
  conversation.previousStatus = previousStatus; // Store for audit purposes

  return await conversation.save();
};

module.exports = mongoose.model('Conversation', conversationSchema);
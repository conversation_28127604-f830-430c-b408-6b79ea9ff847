import { createApi, fetchBaseQuery } from '@reduxjs/toolkit/query/react';

const chatApiBaseUrl =
  import.meta.env.VITE_CHAT_API_BASE_URL || 'http://localhost:8004/';

export interface User {
  id: string;
  type: 'nurse' | 'patient';
  name: string;
  email?: string;
}

export interface Conversation {
  id: string;
  patientId: string;
  nurseId: string;
  patientName: string;
  nurseName: string;
  lastMessage?: string;
  lastMessageTime?: string;
  unreadCount?: number;
  status: 'active' | 'inactive' | 'archived';
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  senderType: 'nurse' | 'patient';
  senderName: string;
  content: string;
  type: 'text' | 'image' | 'file';
  status: 'sent' | 'delivered' | 'read';
  timestamp: string;
  metadata?: Record<string, unknown>;
}

export interface ConversationResponse {
  success: boolean;
  message?: string;
  data?: {
    conversation: Conversation;
  };
  conversation?: Conversation;
}

export interface ConversationsResponse {
  success: boolean;
  message?: string;
  data?: {
    conversations: Conversation[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  conversations?: Conversation[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface MessagesResponse {
  success: boolean;
  message?: string;
  data?: {
    messages: Message[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
  messages?: Message[];
  pagination?: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface SendMessageResponse {
  success: boolean;
  message?: string;
  data?: {
    message: Message;
  };
  messageData?: Message;
  error?: string;
}

export interface SearchMessagesResponse {
  success: boolean;
  messages: Message[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  message?: string;
}

export interface WebSocketStatus {
  connected: boolean;
  connectionId?: string;
  error?: string;
}

export interface CreateConversationRequest {
  nurseId: string;
  nurseName: string;
  initialMessage?: string;
}

export interface SendMessageRequest {
  conversationId: string;
  content: string;
  type?: 'text' | 'image' | 'file';
  metadata?: Record<string, unknown>;
}

export interface GetMessagesParams {
  conversationId: string;
  page?: number;
  limit?: number;
}

export interface GetConversationsParams {
  page?: number;
  limit?: number;
  status?: 'active' | 'inactive' | 'archived';
}

export interface SearchMessagesParams {
  conversationId: string;
  query: string;
  page?: number;
  limit?: number;
}

export const chatApiSlice = createApi({
  reducerPath: 'chatApi',
  baseQuery: fetchBaseQuery({
    baseUrl: chatApiBaseUrl,
    prepareHeaders: (headers, { getState: _getState }) => {
      const token = localStorage.getItem('idToken');
      if (token) {
        headers.set('authorization', `Bearer ${token}`);
      }
      return headers;
    },
  }),
  tagTypes: ['Conversation', 'Message', 'WebSocket'],
  endpoints: builder => ({
    getConversations: builder.query<
      ConversationsResponse,
      GetConversationsParams | void
    >({
      query: (params = {}) => {
        const searchParams = new URLSearchParams();
        if (
          params &&
          typeof params === 'object' &&
          'page' in params &&
          params.page
        )
          searchParams.append('page', params.page.toString());
        if (
          params &&
          typeof params === 'object' &&
          'limit' in params &&
          params.limit
        )
          searchParams.append('limit', params.limit.toString());
        if (
          params &&
          typeof params === 'object' &&
          'status' in params &&
          params.status
        )
          searchParams.append('status', params.status);

        return {
          url: `/api/chat/conversations?${searchParams.toString()}`,
          method: 'GET',
        };
      },
      providesTags: result =>
        result
          ? [
              ...result.conversations.map(({ id }) => ({
                type: 'Conversation' as const,
                id,
              })),
              { type: 'Conversation', id: 'LIST' },
            ]
          : [{ type: 'Conversation', id: 'LIST' }],
      transformErrorResponse: response => {
        console.error('Error fetching conversations:', response);
        return response;
      },
    }),

    getConversation: builder.query<ConversationResponse, string>({
      query: conversationId => {
        const url = `/api/chat/conversations/${conversationId}`;

        return {
          url,
          method: 'GET',
        };
      },
      providesTags: (result, error, conversationId) => [
        { type: 'Conversation', id: conversationId },
      ],
      transformErrorResponse: response => {
        console.error('Error fetching conversation:', response);
        return response;
      },
    }),

    createConversation: builder.mutation<
      ConversationResponse,
      CreateConversationRequest
    >({
      query: data => ({
        url: '/api/chat/conversations',
        method: 'POST',
        body: data,
      }),
      invalidatesTags: [{ type: 'Conversation', id: 'LIST' }],
      transformErrorResponse: response => {
        console.error('Error creating conversation:', response);
        return response;
      },
    }),

    getMessages: builder.query<MessagesResponse, GetMessagesParams>({
      query: ({ conversationId, page = 1, limit = 50 }) => {
        const searchParams = new URLSearchParams();
        searchParams.append('page', page.toString());
        searchParams.append('limit', limit.toString());

        const url = `/api/chat/conversations/${conversationId}/messages?${searchParams.toString()}`;

        return {
          url,
          method: 'GET',
        };
      },
      providesTags: (result, error, { conversationId }) => [
        { type: 'Message', id: conversationId },
      ],
      transformErrorResponse: response => {
        console.error('Error fetching messages:', response);
        return response;
      },
    }),

    sendMessage: builder.mutation<SendMessageResponse, SendMessageRequest>({
      query: ({ conversationId, ...data }) => ({
        url: `/api/chat/conversations/${conversationId}/messages`,
        method: 'POST',
        body: data,
      }),
      invalidatesTags: (result, error, { conversationId }) => [
        { type: 'Message', id: conversationId },
        { type: 'Conversation', id: conversationId },
        { type: 'Conversation', id: 'LIST' },
      ],
      transformErrorResponse: response => {
        console.error('Error sending message:', response);
        return response;
      },
    }),

    searchMessages: builder.query<SearchMessagesResponse, SearchMessagesParams>(
      {
        query: ({ conversationId, query, page = 1, limit = 20 }) => {
          const searchParams = new URLSearchParams();
          searchParams.append('query', query);
          searchParams.append('page', page.toString());
          searchParams.append('limit', limit.toString());

          return {
            url: `/api/chat/conversations/${conversationId}/search?${searchParams.toString()}`,
            method: 'GET',
          };
        },
        transformErrorResponse: response => {
          console.error('Error searching messages:', response);
          return response;
        },
      }
    ),

    markMessagesAsRead: builder.mutation<{ success: boolean }, string>({
      query: conversationId => ({
        url: `/api/chat/conversations/${conversationId}/messages/read`,
        method: 'POST',
      }),
      invalidatesTags: (result, error, conversationId) => [
        { type: 'Message', id: conversationId },
        { type: 'Conversation', id: conversationId },
        { type: 'Conversation', id: 'LIST' },
      ],
      transformErrorResponse: response => {
        console.error('Error marking messages as read:', response);
        return response;
      },
    }),

    updateConversationStatus: builder.mutation<
      ConversationResponse,
      { conversationId: string; status: 'active' | 'inactive' | 'archived' }
    >({
      query: ({ conversationId, status }) => ({
        url: `/api/chat/conversations/${conversationId}/status`,
        method: 'PUT',
        body: { status },
      }),
      invalidatesTags: (result, error, { conversationId }) => [
        { type: 'Conversation', id: conversationId },
        { type: 'Conversation', id: 'LIST' },
      ],
      transformErrorResponse: response => {
        console.error('Error updating conversation status:', response);
        return response;
      },
    }),

    getWebSocketStatus: builder.query<WebSocketStatus, void>({
      query: () => ({
        url: '/api/chat/websocket/status',
        method: 'GET',
      }),
      providesTags: ['WebSocket'],
      transformErrorResponse: response => {
        console.error('Error getting WebSocket status:', response);
        return response;
      },
    }),

    getNurseInfo: builder.query<
      { nurse: { id: string; name: string; profileImage?: string } },
      string
    >({
      query: nurseId => ({
        url: `/api/chat/nurses/${nurseId}`,
        method: 'GET',
      }),
      transformErrorResponse: response => {
        console.error('Error getting nurse info:', response);
        return response;
      },
    }),

    getUnreadCount: builder.query<{ unreadCount: number }, void>({
      query: () => ({
        url: '/api/chat/conversations',
        method: 'GET',
      }),
      transformResponse: (response: ConversationsResponse) => {
        if (response.success && response.data?.conversations) {
          const totalUnreadCount = response.data.conversations.reduce(
            (total: number, conversation: Conversation) =>
              total + (conversation.unreadCount || 0),
            0
          );
          return { unreadCount: totalUnreadCount };
        }
        return { unreadCount: 0 };
      },
      providesTags: ['Message', 'Conversation'],
      transformErrorResponse: response => {
        console.error('Error getting unread count:', response);
        return response;
      },
    }),
  }),
});

export const {
  useGetConversationsQuery,
  useGetConversationQuery,
  useCreateConversationMutation,
  useGetMessagesQuery,
  useSendMessageMutation,
  useSearchMessagesQuery,
  useMarkMessagesAsReadMutation,
  useUpdateConversationStatusMutation,
  useGetWebSocketStatusQuery,
  useGetNurseInfoQuery,
  useGetUnreadCountQuery,
} = chatApiSlice;

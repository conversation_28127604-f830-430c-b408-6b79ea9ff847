const mongoose = require('mongoose');

const messageSchema = new mongoose.Schema({
  conversationId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Conversation',
    required: true,
    index: true
  },
  senderId: {
    type: String,
    required: true,
    index: true
  },
  senderType: {
    type: String,
    enum: ['patient', 'nurse'],
    required: true
  },
  senderName: {
    type: String,
    default: null
  },
  message: {
    type: String,
    required: true
  },
  messageType: {
    type: String,
    enum: ['text', 'image', 'file', 'audio', 'video'],
    default: 'text'
  },
  status: {
    type: String,
    enum: ['sent', 'delivered', 'read'],
    default: 'sent'
  },
  readAt: {
    type: Date,
    default: null
  },
  metadata: {
    type: Map,
    of: String,
    default: {}
  },
  // For file attachments
  attachment: {
    url: String,
    filename: String,
    size: Number,
    mimeType: String
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Compound indexes for efficient queries
messageSchema.index({ conversationId: 1, createdAt: -1 });
messageSchema.index({ senderId: 1, createdAt: -1 });
messageSchema.index({ conversationId: 1, status: 1 });

// Text index for search functionality
messageSchema.index({ message: 'text' });

// Virtual for conversation
messageSchema.virtual('conversation', {
  ref: 'Conversation',
  localField: 'conversationId',
  foreignField: '_id',
  justOne: true
});

// Pre-save middleware to update conversation message count
messageSchema.pre('save', async function(next) {
  if (this.isNew) {
    const Conversation = mongoose.model('Conversation');
    await Conversation.findByIdAndUpdate(
      this.conversationId,
      { 
        $inc: { messageCount: 1 },
        lastMessageAt: new Date()
      }
    );
  }
  next();
});

// Static method to send message
messageSchema.statics.sendMessage = async function(conversationId, senderId, senderType, message, messageType = 'text', senderName = null, attachment = null) {
  const messageData = {
    conversationId,
    senderId,
    senderType,
    senderName,
    message,
    messageType
  };

  if (attachment) {
    messageData.attachment = attachment;
  }

  const newMessage = new this(messageData);
  return await newMessage.save();
};

// Static method to get messages with pagination
messageSchema.statics.getMessages = async function(conversationId, page = 1, limit = 50) {
  const skip = (page - 1) * limit;
  
  return await this.find({ conversationId })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('conversation')
    .lean()
    .then(messages => messages.reverse()); // Return in chronological order
};

// Static method to search messages
messageSchema.statics.searchMessages = async function(conversationId, searchTerm) {
  return await this.find({
    conversationId,
    $text: { $search: searchTerm }
  })
  .sort({ score: { $meta: 'textScore' } })
  .populate('conversation')
  .lean();
};

// Static method to mark messages as read
messageSchema.statics.markAsRead = async function(conversationId, userId) {
  return await this.updateMany(
    {
      conversationId,
      senderId: { $ne: userId },
      status: { $ne: 'read' }
    },
    {
      status: 'read',
      readAt: new Date()
    }
  );
};

// Static method to get conversation stats
messageSchema.statics.getConversationStats = async function(conversationId) {
  const stats = await this.aggregate([
    { $match: { conversationId: new mongoose.Types.ObjectId(conversationId) } },
    {
      $group: {
        _id: null,
        totalMessages: { $sum: 1 },
        patientMessages: {
          $sum: { $cond: [{ $eq: ['$senderType', 'patient'] }, 1, 0] }
        },
        nurseMessages: {
          $sum: { $cond: [{ $eq: ['$senderType', 'nurse'] }, 1, 0] }
        },
        unreadMessages: {
          $sum: { $cond: [{ $ne: ['$status', 'read'] }, 1, 0] }
        },
        firstMessage: { $min: '$createdAt' },
        lastMessage: { $max: '$createdAt' }
      }
    }
  ]);
  
  return stats[0] || {
    totalMessages: 0,
    patientMessages: 0,
    nurseMessages: 0,
    unreadMessages: 0,
    firstMessage: null,
    lastMessage: null
  };
};

// Static method to get unread message count for a user in a specific conversation
messageSchema.statics.getUnreadCountForUser = async function(conversationId, userId) {
  return await this.countDocuments({
    conversationId,
    senderId: { $ne: userId }, // Messages not sent by the user
    status: { $ne: 'read' } // Messages not read
  });
};

module.exports = mongoose.model('Message', messageSchema); 
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { MessageSquare, Loader2 } from 'lucide-react';
import useWebSocketChat from '@/hooks/useWebSocketChat';
import { useToast } from '@/hooks/use-toast';

interface MessageButtonProps {
  nurseId: string;
  nurseName: string;
  variant?:
    | 'default'
    | 'outline'
    | 'secondary'
    | 'ghost'
    | 'link'
    | 'destructive';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  className?: string;
  fullWidth?: boolean;
  disabled?: boolean;
}

const MessageButton: React.FC<MessageButtonProps> = ({
  nurseId,
  nurseName,
  variant = 'default',
  size = 'default',
  className = '',
  fullWidth = false,
  disabled = false,
}) => {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [isNavigating, setIsNavigating] = useState(false);

  const userId = localStorage.getItem('userId') || '';
  const userGivenName = localStorage.getItem('userGivenName') || 'Patient';
  const userType = 'patient';

  const baseWsUrl =
    import.meta.env.VITE_CHAT_WS_URL || 'wss://chatapi.nurserv.com';
  const wsUrl = baseWsUrl.endsWith('/ws')
    ? baseWsUrl
    : `${baseWsUrl.replace(/\/+$/, '')}/ws`;
  const token = localStorage.getItem('idToken') || '';

  const webSocketChat = useWebSocketChat({
    url: wsUrl,
    token,
    userId,
    userType,
    userName: userGivenName,
    enabled: true,
  });

  const { useCreateConversationMutation, status: wsStatus } = webSocketChat;
  const [createConversation, { isLoading }] = useCreateConversationMutation();

  const isButtonLoading = isLoading || isNavigating;

  const handleClick = async () => {
    if (isButtonLoading || disabled) return;

    try {
      const userId = localStorage.getItem('userId');
      if (!userId) {
        toast({
          title: 'Authentication Required',
          description: 'Please log in to chat with a nurse',
          variant: 'destructive',
        });
        navigate('/login', { state: { returnTo: window.location.pathname } });
        return;
      }

      // Check if WebSocket is connected
      if (!wsStatus.connected) {
        toast({
          title: 'Connection Error',
          description: 'Chat service is not available. Please try again later.',
          variant: 'destructive',
        });
        setIsNavigating(false);
        return;
      }

      setIsNavigating(true);
      const response = await createConversation({
        nurseId,
        nurseName,
      }).unwrap();

      if (response.success && response.data?.conversation) {
        navigate(`/chat/${response.data.conversation.id}`, {
          state: { nurseName, nurseId },
        });
      } else {
        throw new Error(response.message || 'Failed to create conversation');
      }
    } catch (error) {
      console.error('Error starting conversation:', error);
      toast({
        title: 'Chat Error',
        description:
          error instanceof Error
            ? error.message
            : 'Failed to start conversation',
        variant: 'destructive',
      });
      setIsNavigating(false);
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      onClick={handleClick}
      disabled={isButtonLoading || disabled}
      className={`${fullWidth ? 'w-full' : ''} ${className}`}
      aria-label={`Message ${nurseName}`}
    >
      {isButtonLoading ? (
        <Loader2 className='h-4 w-4 mr-2 animate-spin' />
      ) : (
        <MessageSquare className='h-4 w-4 mr-2' />
      )}
      Message
    </Button>
  );
};

export default MessageButton;
